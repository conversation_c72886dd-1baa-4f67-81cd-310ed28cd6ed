@import "tailwindcss";

:root {
  --background: 0 0% 100%; /* #FFFFFF */
  --foreground: 0 0% 8.6%; /* #161616 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 0 0% 8.6%; /* #161616 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 0 0% 8.6%; /* #161616 */
  --primary: 134 29% 29%; /* #39744a */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary: 89 25% 52%; /* #81a66a */
  --secondary-foreground: 0 0% 8.6%; /* #161616 */
  --muted: 0 0% 96%; /* #F5F5F5 */
  --muted-foreground: 0 0% 71.4%; /* #b6b6b6 */
  --accent: 89 25% 52%; /* #81a66a */
  --accent-foreground: 0 0% 8.6%; /* #161616 */
  --destructive: 0 84.2% 60.2%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --border: 0 0% 89.8%; /* #E5E5E5 */
  --input: 0 0% 89.8%; /* #E5E5E5 */
  --ring: 134 29% 29%; /* #39744a */
  --radius: 0.5rem;

  /* Custom Fleet Colors */
  --fleet-grey: 0 0% 58.4%; /* #959393 */
  --fleet-light-mud: 48 100% 87.1%; /* #fff2bd */
  --fleet-mud: 45 56% 57.1%; /* #cead57 */
  --fleet-brown: 45 85% 19.6%; /* #654a08 */
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 0 0% 8.6%; /* #161616 */
    --foreground: 0 0% 100%; /* #FFFFFF */
    --card: 0 0% 12%; /* #1F1F1F */
    --card-foreground: 0 0% 100%; /* #FFFFFF */
    --popover: 0 0% 12%; /* #1F1F1F */
    --popover-foreground: 0 0% 100%; /* #FFFFFF */
    --primary: 134 29% 35%; /* #4a8a5a - lighter version for dark mode */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */
    --secondary: 89 25% 45%; /* #6d9157 - darker version for dark mode */
    --secondary-foreground: 0 0% 100%; /* #FFFFFF */
    --muted: 0 0% 15%; /* #262626 */
    --muted-foreground: 0 0% 58.4%; /* #959393 */
    --accent: 89 25% 45%; /* #6d9157 */
    --accent-foreground: 0 0% 100%; /* #FFFFFF */
    --destructive: 0 62.8% 50%; /* #DC2626 */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF */
    --border: 0 0% 20%; /* #333333 */
    --input: 0 0% 20%; /* #333333 */
    --ring: 134 29% 35%; /* #4a8a5a */

    /* Custom Fleet Colors for Dark Mode */
    --fleet-grey: 0 0% 65%; /* #A6A6A6 - lighter for dark mode */
    --fleet-light-mud: 48 80% 75%; /* #f0e09a - darker for dark mode */
    --fleet-mud: 45 50% 50%; /* #b8984a - adjusted for dark mode */
    --fleet-brown: 45 70% 30%; /* #8a6b0f - lighter for dark mode */
  }
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

* {
  border-color: hsl(var(--border));
}
